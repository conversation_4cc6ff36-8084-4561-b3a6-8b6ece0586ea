'use client'

import React from 'react'
import { useCollapseState } from '@/lib/stores/window-store'
import { useFloatingWindowStore } from '@/lib/stores/floating-window-store'
import Image from 'next/image'
import { DarkTooltip } from './DarkTooltip'

interface CollapsedQuickAccessProps {
  onAppLaunch?: (appName: string) => void
}

// ZiExplorer Panel - EXACTLY like middle section (same size, same style)
export function CollapsedQuickAccess({}: CollapsedQuickAccessProps) {
  const { shouldCollapse } = useCollapseState()
  const { openWindow } = useFloatingWindowStore()

  if (!shouldCollapse) return null

  const handleZiExplorerClick = () => {
    // Open in original desktop position with optimal height to prevent scrolling
    // Increased height to 600px for excellent content visibility without scrolling
    const screenHeight = window.innerHeight
    const defaultHeight = Math.min(600, screenHeight - 150) // Ensure it fits on screen with more content space

    openWindow({
      id: 'ziexplorer',
      title: 'ZiExplorer',
      content: 'ziexplorer',
      position: { x: 48, y: 96 },
      size: { width: 320, height: defaultHeight },
      windowType: 'panel'
    })
  }



  return (
    <div className="relative" style={{ overflow: 'visible' }}>
      {/* Redesigned ZiExplorer - Sleek Docked Style */}
      <div className="flex items-center justify-center" style={{ overflow: 'visible' }}>
        <DarkTooltip content="ZiExplorer - File Manager">
          <button
          onClick={handleZiExplorerClick}
          className="relative group transition-all duration-300 ease-out"
          style={{
            overflow: 'visible'
          }}
        >
          {/* Main Icon Container - Bigger Size */}
          <div
            className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ease-out"
            style={{
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.12) 0%,
                  rgba(255, 255, 255, 0.06) 50%,
                  rgba(255, 255, 255, 0.08) 100%
                ),
                linear-gradient(135deg,
                  rgba(99, 102, 241, 0.25) 0%,
                  rgba(139, 92, 246, 0.22) 25%,
                  rgba(110, 140, 160, 0.20) 50%,
                  rgba(90, 150, 140, 0.18) 75%,
                  rgba(59, 130, 246, 0.15) 100%
                )
              `,
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.25),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1)
              `,
              backdropFilter: 'blur(12px) saturate(1.2)',
              transform: 'scale(1)',
              transformOrigin: 'center'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'scale(1.15) translateY(-2px)'
              e.currentTarget.style.boxShadow = `
                0 6px 20px rgba(0, 0, 0, 0.25),
                0 4px 12px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.35),
                inset 0 -1px 0 rgba(0, 0, 0, 0.15),
                0 0 20px rgba(99, 102, 241, 0.3)
              `
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1) translateY(0)'
              e.currentTarget.style.boxShadow = `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.25),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1)
              `
            }}
          >
            <div className="w-8 h-8 relative">
              <Image
                src="/images/icons/documents-folder.png"
                alt="ZiExplorer"
                width={32}
                height={32}
                className="object-contain filter drop-shadow-sm"
              />
            </div>
          </div>

          </button>
        </DarkTooltip>
      </div>
    </div>
  )
}

// ZiWidgets Panel - Bigger, more visible with original widget arrangement
export function CollapsedZiWidgets() {
  const { shouldCollapse } = useCollapseState()
  const { openWindow } = useFloatingWindowStore()

  if (!shouldCollapse) return null

  const handleZiWidgetsClick = () => {
    // Open in EXACT original desktop position from DesktopWidgets
    // Original position: { x: window.innerWidth - 420, y: 100 }
    // Original size: { width: 350, height: window.innerHeight - 280 }
    // Ensure it stays within desktop bounds and doesn't slide
    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight
    const panelWidth = 350
    const panelHeight = screenHeight - 280

    // Calculate exact position to align with desktop edge (not taskbar)
    const originalX = screenWidth - 420 // Exact original position
    const originalY = 100 // Exact original Y position

    // Ensure the window doesn't go outside desktop bounds
    const safeX = Math.max(20, Math.min(originalX, screenWidth - panelWidth - 20))
    const safeY = Math.max(20, originalY)
    const safeHeight = Math.min(panelHeight, screenHeight - 120) // Leave space for taskbar

    openWindow({
      id: 'ziwidgets',
      title: 'ZiWidgets',
      content: 'ziwidgets',
      position: { x: safeX, y: safeY },
      size: { width: panelWidth, height: safeHeight },
      windowType: 'panel'
    })
  }



  return (
    <div className="flex items-center space-x-3 mr-4" style={{ overflow: 'visible' }}>
      {/* Single ZiWidgets Icon - Bigger and More Visible */}
      <div className="relative" style={{ overflow: 'visible' }}>
        <DarkTooltip content="ZiWidgets - System Tools">
          <button
            onClick={handleZiWidgetsClick}
            className="relative w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ease-out group"
            style={{
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.18) 0%,
                  rgba(255, 255, 255, 0.10) 50%,
                  rgba(255, 255, 255, 0.15) 100%
                ),
                linear-gradient(135deg,
                  rgba(99, 102, 241, 0.25) 0%,
                  rgba(139, 92, 246, 0.22) 25%,
                  rgba(110, 140, 160, 0.20) 50%,
                  rgba(90, 150, 140, 0.18) 75%,
                  rgba(59, 130, 246, 0.15) 100%
                )
              `,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.35),
                inset 0 -1px 0 rgba(0, 0, 0, 0.12)
              `,
              backdropFilter: 'blur(12px) saturate(1.2)',
              color: 'rgba(255, 255, 255, 0.9)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'scale(1.15) translateY(-2px)'
              e.currentTarget.style.boxShadow = `
                0 6px 20px rgba(0, 0, 0, 0.25),
                0 4px 12px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.45),
                inset 0 -1px 0 rgba(0, 0, 0, 0.18),
                0 0 20px rgba(99, 102, 241, 0.35)
              `
              e.currentTarget.style.color = 'rgba(255, 255, 255, 1)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1) translateY(0)'
              e.currentTarget.style.boxShadow = `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.35),
                inset 0 -1px 0 rgba(0, 0, 0, 0.12)
              `
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.9)'
            }}
          >
            {/* ZiWidgets Grid Icon */}
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="7" height="7"/>
              <rect x="14" y="3" width="7" height="7"/>
              <rect x="14" y="14" width="7" height="7"/>
              <rect x="3" y="14" width="7" height="7"/>
            </svg>
          </button>
        </DarkTooltip>
      </div>

    </div>
  )
}
