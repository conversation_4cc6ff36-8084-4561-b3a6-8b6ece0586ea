'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useFloatingWindowStore } from '@/lib/stores/floating-window-store'
import { DarkTooltip } from './DarkTooltip'

export function MinimizedWindows() {
  const { windows, restoreWindow } = useFloatingWindowStore()
  
  // Get minimized windows
  const minimizedWindows = windows.filter(window => window.isOpen && window.isMinimized)

  if (minimizedWindows.length === 0) return null

  return (
    <div className="flex items-center space-x-2 mr-4">
      <AnimatePresence>
        {minimizedWindows.map((window) => (
          <motion.div
            key={window.id}
            initial={{ scale: 0, opacity: 0, x: -20 }}
            animate={{ scale: 1, opacity: 1, x: 0 }}
            exit={{ scale: 0, opacity: 0, x: -20 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            <DarkTooltip content={`Restore ${window.title}`}>
              <button
                onClick={() => restoreWindow(window.id)}
                className="relative w-10 h-6 rounded-md transition-all duration-300 ease-out group"
                style={{
                  background: `
                    linear-gradient(135deg,
                      rgba(255, 255, 255, 0.12) 0%,
                      rgba(255, 255, 255, 0.06) 50%,
                      rgba(255, 255, 255, 0.08) 100%
                    ),
                    linear-gradient(135deg,
                      rgba(99, 102, 241, 0.20) 0%,
                      rgba(139, 92, 246, 0.18) 25%,
                      rgba(110, 140, 160, 0.16) 50%,
                      rgba(90, 150, 140, 0.14) 75%,
                      rgba(59, 130, 246, 0.12) 100%
                    )
                  `,
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: `
                    0 2px 6px rgba(0, 0, 0, 0.1),
                    0 1px 3px rgba(0, 0, 0, 0.08),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2),
                    inset 0 -1px 0 rgba(0, 0, 0, 0.1)
                  `,
                  backdropFilter: 'blur(8px) saturate(1.1)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'scale(1.05) translateY(-1px)'
                  e.currentTarget.style.boxShadow = `
                    0 4px 12px rgba(0, 0, 0, 0.15),
                    0 2px 6px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3),
                    inset 0 -1px 0 rgba(0, 0, 0, 0.15),
                    0 0 15px rgba(99, 102, 241, 0.25)
                  `
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'scale(1) translateY(0)'
                  e.currentTarget.style.boxShadow = `
                    0 2px 6px rgba(0, 0, 0, 0.1),
                    0 1px 3px rgba(0, 0, 0, 0.08),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2),
                    inset 0 -1px 0 rgba(0, 0, 0, 0.1)
                  `
                }}
              >
                {/* Window Icon */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-6 h-3 rounded-sm border border-white/30 bg-white/10">
                    {/* Title Bar */}
                    <div className="w-full h-1 bg-white/20 rounded-t-sm"></div>
                    {/* Content Area */}
                    <div className="w-full h-2 bg-white/5 rounded-b-sm"></div>
                  </div>
                </div>

                {/* Window Title */}
                <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 text-xs text-white/60 font-medium truncate max-w-[40px]">
                  {window.title.slice(0, 6)}
                </div>

                {/* Active Indicator */}
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-400 rounded-full opacity-80"></div>
              </button>
            </DarkTooltip>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}
