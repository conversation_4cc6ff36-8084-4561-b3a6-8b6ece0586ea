'use client'

import React, { useState, cloneElement } from 'react'
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useHover,
  useFocus,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal
} from '@floating-ui/react'
import { motion, AnimatePresence } from 'framer-motion'

interface TooltipProps {
  content: string
  children: React.ReactElement
  placement?: 'top' | 'bottom' | 'left' | 'right'
  delay?: number
  disabled?: boolean
}

export default function Tooltip({ 
  content, 
  children, 
  placement = 'top',
  delay = 200,
  disabled = false 
}: TooltipProps) {
  const [isOpen, setIsOpen] = useState(false)

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen && !disabled,
    onOpenChange: setIsOpen,
    placement,
    middleware: [
      offset(8),
      flip(),
      shift({ padding: 8 })
    ],
    whileElementsMounted: autoUpdate,
  })

  const hover = useHover(context, {
    move: false,
    delay: { open: delay, close: 100 }
  })
  const focus = useFocus(context)
  const dismiss = useDismiss(context)
  const role = useRole(context, { role: 'tooltip' })

  const { getReferenceProps, getFloatingProps } = useInteractions([
    hover,
    focus,
    dismiss,
    role,
  ])

  return (
    <>
      {cloneElement(children, {
        ref: refs.setReference,
        ...getReferenceProps()
      })}
      
      <FloatingPortal>
        <AnimatePresence>
          {isOpen && !disabled && (
            <motion.div
              ref={refs.setFloating}
              style={floatingStyles}
              {...getFloatingProps()}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.15, ease: "easeOut" }}
              className="px-3 py-2 text-sm font-medium text-white rounded-lg shadow-lg pointer-events-none z-50"
              style={{
                ...floatingStyles,
                background: `
                  linear-gradient(135deg,
                    rgba(0, 0, 0, 0.9) 0%,
                    rgba(0, 0, 0, 0.8) 100%
                  )
                `,
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: `
                  0 4px 12px rgba(0, 0, 0, 0.4),
                  0 2px 6px rgba(0, 0, 0, 0.2),
                  inset 0 1px 0 rgba(255, 255, 255, 0.15)
                `,
                backdropFilter: 'blur(8px)',
                maxWidth: '200px',
                wordWrap: 'break-word'
              }}
            >
              {content}
            </motion.div>
          )}
        </AnimatePresence>
      </FloatingPortal>
    </>
  )
}
